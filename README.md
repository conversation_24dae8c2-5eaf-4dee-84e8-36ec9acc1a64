# QMT格力电器数据获取工具

本项目用于通过QMT（量化交易平台）获取格力电器在2025年7月30日9:30的股票数据。

## 功能特性

- 连接QMT量化交易平台
- 获取格力电器(000651.SZ)的实时行情数据
- 获取指定时间点的分时数据
- 获取基本面和财务数据
- 数据格式化显示和保存

## 环境要求

### 必需软件
1. **QMT量化交易平台** - 需要先安装并配置QMT客户端
2. **Python 3.7+** - 推荐使用Python 3.8或更高版本
3. **QMT Python SDK** - 通过QMT客户端安装

### Python依赖
```bash
pip install -r requirements.txt
```

## QMT环境配置

### 1. 安装QMT客户端
- 从官方渠道下载并安装QMT量化交易平台
- 完成账户注册和认证
- 确保QMT客户端正常运行

### 2. 配置Python环境
QMT通常会自带Python环境和相关模块，需要：
- 找到QMT安装目录下的Python环境
- 将QMT的Python路径添加到系统环境变量
- 或者在QMT提供的Python环境中运行脚本

### 3. 验证QMT连接
```python
from xtquant import xtdata
session_id = xtdata.connect()
print(f"QMT连接状态: {session_id}")
```

## 使用方法

### 基本使用
```bash
python qmt_gree_data.py
```

### 自定义参数
可以修改 `qmt_config.py` 中的配置：
- 目标日期和时间
- 数据获取参数
- 输出格式设置

## 文件说明

- `qmt_gree_data.py` - 主程序文件
- `qmt_config.py` - 配置文件
- `requirements.txt` - Python依赖列表
- `README.md` - 使用说明

## 数据输出

程序会输出以下数据：

### 市场行情数据
- 开盘价、最高价、最低价、收盘价
- 成交量、成交额
- 实时价格变动

### 基本信息
- 股票代码和名称
- 交易所信息
- 行业分类

### 数据保存
- 自动保存为JSON格式文件
- 文件名包含时间戳
- 保存在当前目录

## 注意事项

1. **QMT连接**：确保QMT客户端正在运行且网络连接正常
2. **交易时间**：股票数据只在交易时间内更新
3. **历史数据**：2025年7月30日的数据需要等到该日期才能获取实时数据
4. **权限要求**：某些数据可能需要相应的数据权限

## 故障排除

### 常见问题

1. **ImportError: No module named 'xtquant'**
   - 确保在QMT提供的Python环境中运行
   - 检查QMT是否正确安装

2. **连接失败**
   - 检查QMT客户端是否运行
   - 验证网络连接
   - 确认账户权限

3. **数据为空**
   - 检查股票代码是否正确
   - 确认查询时间是否在交易时间内
   - 验证数据权限

## 技术支持

如遇到问题，请检查：
1. QMT客户端运行状态
2. Python环境配置
3. 网络连接状态
4. 数据权限设置

## 免责声明

本工具仅用于数据获取和学习目的，不构成投资建议。使用者应当遵守相关法律法规和交易所规则。
