#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import datetime
import json
import sys
import traceback

def log_message(message):
    """Print message with timestamp"""
    timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    print(f"[{timestamp}] {message}")

def main():
    log_message("Starting QMT Gree Electric Data Retrieval")
    log_message("Target: Get Gree Electric data for 2025-07-30 09:30")
    log_message("=" * 60)
    
    # Step 1: Import QMT modules
    log_message("Step 1: Importing QMT modules...")
    try:
        from xtquant import xtdata
        log_message("SUCCESS: QMT modules imported successfully")
    except ImportError as e:
        log_message(f"FAILED: QMT import failed: {e}")
        log_message("Please ensure QMT is installed and configured properly")
        return False
    except Exception as e:
        log_message(f"UNEXPECTED ERROR during import: {e}")
        traceback.print_exc()
        return False
    
    # Step 2: Connect to QMT
    log_message("Step 2: Connecting to QMT...")
    try:
        session_id = xtdata.connect()
        log_message(f"Connection attempt result - Session ID: {session_id}")
        
        if session_id <= 0:
            log_message("FAILED: QMT connection failed")
            log_message("Possible reasons:")
            log_message("1. QMT client is not running")
            log_message("2. Network connection issues")
            log_message("3. Account not logged in")
            log_message("4. QMT service not available")
            return False
        
        log_message(f"SUCCESS: QMT connected successfully. Session ID: {session_id}")
        
    except Exception as e:
        log_message(f"ERROR during QMT connection: {e}")
        traceback.print_exc()
        return False
    
    # Step 3: Test basic QMT functionality
    log_message("Step 3: Testing basic QMT functionality...")
    try:
        # Try to get some basic market info
        log_message("Testing get_market_data function...")
        
        # Gree Electric stock code
        gree_code = "000651.SZ"
        log_message(f"Target stock: {gree_code} (Gree Electric)")
        
        # Get current date for testing
        current_date = datetime.datetime.now().strftime("%Y%m%d")
        log_message(f"Using current date for testing: {current_date}")
        
        # Step 4: Get stock basic info
        log_message("Step 4: Getting stock basic information...")
        try:
            stock_info = xtdata.get_instrument_detail(gree_code)
            if stock_info:
                log_message("SUCCESS: Stock basic info retrieved")
                log_message(f"  Stock Code: {gree_code}")
                log_message(f"  Stock Name: {stock_info.get('InstrumentName', 'N/A')}")
                log_message(f"  Exchange: {stock_info.get('ExchangeID', 'N/A')}")
                log_message(f"  Full info keys: {list(stock_info.keys()) if isinstance(stock_info, dict) else 'Not a dict'}")
            else:
                log_message("WARNING: No stock basic info returned")
                stock_info = None
        except Exception as e:
            log_message(f"ERROR getting stock info: {e}")
            traceback.print_exc()
            stock_info = None
        
        # Step 5: Get market data
        log_message("Step 5: Getting market data...")
        try:
            log_message("Attempting to get market data with parameters:")
            log_message(f"  stock_list: [{gree_code}]")
            log_message(f"  period: '1d'")
            log_message(f"  count: 10")
            
            market_data = xtdata.get_market_data(
                stock_list=[gree_code],
                period='1d',  # Daily data
                count=10      # Last 10 days
            )
            
            log_message(f"Market data result type: {type(market_data)}")
            log_message(f"Market data keys: {list(market_data.keys()) if isinstance(market_data, dict) else 'Not a dict'}")
            
            if market_data and gree_code in market_data:
                data = market_data[gree_code]
                log_message("SUCCESS: Market data retrieved")
                log_message(f"  Data type: {type(data)}")
                log_message(f"  Data keys: {list(data.keys()) if isinstance(data, dict) else 'Not a dict'}")
                
                # Check if we have price data
                if isinstance(data, dict) and 'close' in data:
                    close_prices = data['close']
                    log_message(f"  Close prices count: {len(close_prices) if close_prices else 0}")
                    if close_prices and len(close_prices) > 0:
                        log_message(f"  Latest close price: {close_prices[-1]}")
                        log_message(f"  Open prices: {data.get('open', 'N/A')}")
                        log_message(f"  High prices: {data.get('high', 'N/A')}")
                        log_message(f"  Low prices: {data.get('low', 'N/A')}")
                        log_message(f"  Volume: {data.get('volume', 'N/A')}")
                else:
                    log_message("  No close price data found")
            else:
                log_message("WARNING: No market data returned for the stock")
                market_data = None
                
        except Exception as e:
            log_message(f"ERROR getting market data: {e}")
            traceback.print_exc()
            market_data = None
        
        # Step 6: Save results
        log_message("Step 6: Saving results...")
        try:
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"gree_debug_{timestamp}.json"
            
            result_data = {
                'timestamp': datetime.datetime.now().isoformat(),
                'stock_code': gree_code,
                'company_name': 'Gree Electric',
                'target_date': '20250730',
                'target_time': '09:30:00',
                'session_id': session_id,
                'stock_info': stock_info,
                'market_data': market_data.get(gree_code) if market_data else None,
                'test_status': 'completed'
            }
            
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(result_data, f, ensure_ascii=False, indent=2, default=str)
            
            log_message(f"SUCCESS: Results saved to {filename}")
            
        except Exception as e:
            log_message(f"ERROR saving results: {e}")
            traceback.print_exc()
        
    except Exception as e:
        log_message(f"UNEXPECTED ERROR during testing: {e}")
        traceback.print_exc()
        return False
    
    finally:
        # Step 7: Disconnect
        log_message("Step 7: Disconnecting from QMT...")
        try:
            xtdata.disconnect()
            log_message("SUCCESS: QMT connection closed")
        except Exception as e:
            log_message(f"ERROR during disconnect: {e}")
    
    log_message("=" * 60)
    log_message("Debug session completed")
    return True

if __name__ == "__main__":
    try:
        success = main()
        if success:
            print("\nDEBUG COMPLETED SUCCESSFULLY")
        else:
            print("\nDEBUG COMPLETED WITH ERRORS")
    except Exception as e:
        print(f"\nUNEXPECTED ERROR: {e}")
        traceback.print_exc()
