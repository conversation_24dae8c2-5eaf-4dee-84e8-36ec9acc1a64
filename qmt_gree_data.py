#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
QMT Data Retrieval Script for Gree Electric Appliances
获取格力电器在2025年7月30日9:30的数据
"""

import datetime
import pandas as pd
import time
import sys
import os

# Set UTF-8 encoding for console output
if sys.platform.startswith('win'):
    import codecs
    sys.stdout = codecs.getwriter('utf-8')(sys.stdout.detach())
    sys.stderr = codecs.getwriter('utf-8')(sys.stderr.detach())

try:
    # QMT API imports - these need to be installed with QMT
    from xtquant import xtdata
    from xtquant.xttrader import XtQuantTrader
    from xtquant.xttype import StockAccount
except ImportError as e:
    print(f"QMT modules not found: {e}")
    print("Please ensure QMT is installed and Python environment is properly configured")
    print("QMT模块未找到，请确保QMT已安装且Python环境配置正确")
    exit(1)


class QMTDataRetriever:
    """QMT数据获取器"""
    
    def __init__(self):
        self.session_id = None
        self.trader = None
        
    def connect_qmt(self):
        """连接QMT"""
        try:
            # 连接QMT
            self.session_id = xtdata.connect()
            if self.session_id > 0:
                print(f"QMT连接成功，会话ID: {self.session_id}")
                return True
            else:
                print("QMT连接失败")
                return False
        except Exception as e:
            print(f"连接QMT时出错: {e}")
            return False
    
    def get_gree_data(self, target_date="********", target_time="09:30:00"):
        """
        获取格力电器的数据
        
        Args:
            target_date (str): 目标日期，格式YYYYMMDD
            target_time (str): 目标时间，格式HH:MM:SS
        """
        # 格力电器股票代码
        gree_code = "000651.SZ"  # 格力电器深交所代码
        
        try:
            # 获取基本信息
            print(f"正在获取格力电器({gree_code})在{target_date} {target_time}的数据...")
            
            # 转换日期格式
            target_datetime = datetime.datetime.strptime(f"{target_date} {target_time}", "%Y%m%d %H:%M:%S")
            
            # 获取实时行情数据
            market_data = self.get_market_data(gree_code, target_date, target_time)
            
            # 获取分时数据
            minute_data = self.get_minute_data(gree_code, target_date, target_time)
            
            # 获取基本面数据
            fundamental_data = self.get_fundamental_data(gree_code)
            
            return {
                'stock_code': gree_code,
                'company_name': '格力电器',
                'target_datetime': target_datetime,
                'market_data': market_data,
                'minute_data': minute_data,
                'fundamental_data': fundamental_data
            }
            
        except Exception as e:
            print(f"获取格力电器数据时出错: {e}")
            return None
    
    def get_market_data(self, stock_code, date, time):
        """获取市场行情数据"""
        try:
            # 获取实时行情
            market_data = xtdata.get_market_data(
                stock_list=[stock_code],
                period='1m',
                start_time=date,
                end_time=date,
                count=-1
            )
            
            if market_data and stock_code in market_data:
                data = market_data[stock_code]
                return {
                    'open': data.get('open', []),
                    'high': data.get('high', []),
                    'low': data.get('low', []),
                    'close': data.get('close', []),
                    'volume': data.get('volume', []),
                    'amount': data.get('amount', []),
                    'time': data.get('time', [])
                }
            else:
                print("未获取到市场数据")
                return None
                
        except Exception as e:
            print(f"获取市场数据时出错: {e}")
            return None
    
    def get_minute_data(self, stock_code, date, time):
        """获取分时数据"""
        try:
            # 获取分时数据
            minute_data = xtdata.get_market_data(
                stock_list=[stock_code],
                period='tick',
                start_time=f"{date} 09:30:00",
                end_time=f"{date} 15:00:00",
                count=-1
            )
            
            return minute_data
            
        except Exception as e:
            print(f"获取分时数据时出错: {e}")
            return None
    
    def get_fundamental_data(self, stock_code):
        """获取基本面数据"""
        try:
            # 获取股票基本信息
            stock_info = xtdata.get_instrument_detail(stock_code)
            
            # 获取财务数据
            financial_data = xtdata.get_financial_data(
                stock_list=[stock_code],
                table_list=['Balance', 'Income', 'CashFlow'],
                start_time='',
                end_time='',
                report_type='quarter'
            )
            
            return {
                'stock_info': stock_info,
                'financial_data': financial_data
            }
            
        except Exception as e:
            print(f"获取基本面数据时出错: {e}")
            return None
    
    def format_and_display_data(self, data):
        """格式化并显示数据"""
        if not data:
            print("没有数据可显示")
            return
        
        print("\n" + "="*60)
        print(f"格力电器({data['stock_code']})数据报告")
        print(f"查询时间: {data['target_datetime']}")
        print("="*60)
        
        # 显示市场数据
        if data['market_data']:
            print("\n【市场行情数据】")
            market = data['market_data']
            if market['close'] and len(market['close']) > 0:
                print(f"开盘价: {market['open'][-1] if market['open'] else 'N/A'}")
                print(f"最高价: {market['high'][-1] if market['high'] else 'N/A'}")
                print(f"最低价: {market['low'][-1] if market['low'] else 'N/A'}")
                print(f"收盘价: {market['close'][-1] if market['close'] else 'N/A'}")
                print(f"成交量: {market['volume'][-1] if market['volume'] else 'N/A'}")
                print(f"成交额: {market['amount'][-1] if market['amount'] else 'N/A'}")
        
        # 显示基本面数据
        if data['fundamental_data'] and data['fundamental_data']['stock_info']:
            print("\n【股票基本信息】")
            info = data['fundamental_data']['stock_info']
            print(f"股票名称: {info.get('InstrumentName', 'N/A')}")
            print(f"上市板块: {info.get('ExchangeID', 'N/A')}")
            print(f"行业分类: {info.get('IndustryID', 'N/A')}")
    
    def disconnect_qmt(self):
        """断开QMT连接"""
        try:
            if self.session_id:
                xtdata.disconnect()
                print("QMT连接已断开")
        except Exception as e:
            print(f"断开QMT连接时出错: {e}")


def main():
    """主函数"""
    print("QMT格力电器数据获取程序")
    print("目标: 获取格力电器2025年7月30日9:30的数据")
    
    # 创建数据获取器
    retriever = QMTDataRetriever()
    
    try:
        # 连接QMT
        if not retriever.connect_qmt():
            print("无法连接到QMT，程序退出")
            return
        
        # 获取格力电器数据
        gree_data = retriever.get_gree_data("********", "09:30:00")
        
        # 显示数据
        retriever.format_and_display_data(gree_data)
        
        # 保存数据到文件
        if gree_data:
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"gree_data_{timestamp}.json"
            
            import json
            with open(filename, 'w', encoding='utf-8') as f:
                # 转换datetime对象为字符串以便JSON序列化
                data_to_save = gree_data.copy()
                data_to_save['target_datetime'] = data_to_save['target_datetime'].isoformat()
                json.dump(data_to_save, f, ensure_ascii=False, indent=2)
            
            print(f"\n数据已保存到文件: {filename}")
    
    except Exception as e:
        print(f"程序执行出错: {e}")
    
    finally:
        # 断开连接
        retriever.disconnect_qmt()


if __name__ == "__main__":
    main()
