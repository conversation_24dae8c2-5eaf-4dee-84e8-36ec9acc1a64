#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import datetime
import json
import sys

def main():
    print("QMT Gree Electric Data Retrieval")
    print("Target: Get Gree Electric data for 2025-07-30 09:30")
    print("=" * 50)
    
    try:
        # Import QMT modules
        from xtquant import xtdata
        print("QMT modules imported successfully")
    except ImportError as e:
        print(f"QMT import failed: {e}")
        print("Please ensure QMT is installed and configured properly")
        return
    
    try:
        # Connect to QMT
        print("Connecting to QMT...")
        session_id = xtdata.connect()
        
        if session_id <= 0:
            print(f"QMT connection failed. Session ID: {session_id}")
            print("Please check:")
            print("1. QMT client is running")
            print("2. Network connection")
            print("3. Account login status")
            return
        
        print(f"QMT connected successfully. Session ID: {session_id}")
        
        # Gree Electric stock code
        gree_code = "000651.SZ"
        target_date = "********"
        target_time = "09:30:00"
        
        print(f"Querying Gree Electric ({gree_code}) data...")
        print(f"Target date: {target_date}")
        print(f"Target time: {target_time}")
        
        # Get stock basic info
        try:
            stock_info = xtdata.get_instrument_detail(gree_code)
            if stock_info:
                print("\nStock Basic Info:")
                print(f"  Code: {gree_code}")
                print(f"  Name: {stock_info.get('InstrumentName', 'N/A')}")
                print(f"  Exchange: {stock_info.get('ExchangeID', 'N/A')}")
            else:
                print("Failed to get stock basic info")
        except Exception as e:
            print(f"Error getting stock info: {e}")
        
        # Get market data
        try:
            print("\nGetting market data...")
            market_data = xtdata.get_market_data(
                stock_list=[gree_code],
                period='1m',
                start_time=target_date,
                end_time=target_date,
                count=-1
            )
            
            if market_data and gree_code in market_data:
                data = market_data[gree_code]
                print("Market data retrieved successfully")
                
                # Display latest data if available
                if data.get('close') and len(data['close']) > 0:
                    print("\nLatest Market Data:")
                    print(f"  Open: {data['open'][-1] if data.get('open') else 'N/A'}")
                    print(f"  High: {data['high'][-1] if data.get('high') else 'N/A'}")
                    print(f"  Low: {data['low'][-1] if data.get('low') else 'N/A'}")
                    print(f"  Close: {data['close'][-1] if data.get('close') else 'N/A'}")
                    print(f"  Volume: {data['volume'][-1] if data.get('volume') else 'N/A'}")
                    print(f"  Amount: {data['amount'][-1] if data.get('amount') else 'N/A'}")
                else:
                    print("No market data available for the specified date/time")
            else:
                print("No market data retrieved")
                
        except Exception as e:
            print(f"Error getting market data: {e}")
        
        # Save data to file
        try:
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"gree_data_{timestamp}.json"
            
            result_data = {
                'stock_code': gree_code,
                'company_name': 'Gree Electric',
                'target_date': target_date,
                'target_time': target_time,
                'query_time': datetime.datetime.now().isoformat(),
                'stock_info': stock_info if 'stock_info' in locals() else None,
                'market_data': market_data.get(gree_code) if 'market_data' in locals() and market_data else None
            }
            
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(result_data, f, ensure_ascii=False, indent=2, default=str)
            
            print(f"\nData saved to: {filename}")
            
        except Exception as e:
            print(f"Error saving data: {e}")
        
    except Exception as e:
        print(f"Unexpected error: {e}")
    
    finally:
        # Disconnect from QMT
        try:
            xtdata.disconnect()
            print("QMT connection closed")
        except:
            pass
    
    print("\nProgram completed")

if __name__ == "__main__":
    main()
