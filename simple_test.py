#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import datetime

def main():
    print("QMT Environment Test")
    print("=" * 40)
    print(f"Test time: {datetime.datetime.now()}")
    print(f"Python version: {sys.version}")
    print("=" * 40)
    
    # Test 1: Import QMT modules
    print("\n[Test 1] Testing QMT module import...")
    try:
        from xtquant import xtdata
        from xtquant.xttrader import XtQuantTrader
        print("SUCCESS: QMT modules imported successfully")
        qmt_available = True
    except ImportError as e:
        print(f"FAILED: QMT module import failed: {e}")
        print("Please ensure:")
        print("1. QMT client is properly installed")
        print("2. Run this script in QMT Python environment")
        print("3. QMT Python SDK is properly configured")
        qmt_available = False
    
    if not qmt_available:
        print("\nCannot proceed without QMT modules. Please fix the import issue first.")
        return
    
    # Test 2: QMT connection
    print("\n[Test 2] Testing QMT connection...")
    try:
        session_id = xtdata.connect()
        
        if session_id > 0:
            print(f"SUCCESS: QMT connection successful, session ID: {session_id}")
            
            # Test 3: Get Gree Electric info
            print("\n[Test 3] Testing Gree Electric stock info...")
            gree_code = "000651.SZ"
            print(f"Querying stock code: {gree_code}")
            
            try:
                stock_info = xtdata.get_instrument_detail(gree_code)
                if stock_info:
                    print("SUCCESS: Retrieved Gree Electric info:")
                    print(f"  Stock code: {gree_code}")
                    print(f"  Stock name: {stock_info.get('InstrumentName', 'N/A')}")
                    print(f"  Exchange: {stock_info.get('ExchangeID', 'N/A')}")
                else:
                    print("FAILED: Could not retrieve stock info")
                    
            except Exception as e:
                print(f"FAILED: Error retrieving stock info: {e}")
            
            # Disconnect
            xtdata.disconnect()
            print("\nQMT connection closed")
            
        else:
            print(f"FAILED: QMT connection failed, session ID: {session_id}")
            print("Please check:")
            print("1. Is QMT client running?")
            print("2. Is network connection normal?")
            print("3. Is account logged in?")
            
    except Exception as e:
        print(f"FAILED: QMT connection test error: {e}")
    
    print("\n" + "=" * 40)
    print("Test completed. If all tests passed, you can run:")
    print("python qmt_gree_data.py")
    print("=" * 40)

if __name__ == "__main__":
    main()
