#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
QMT配置文件
Configuration file for QMT data retrieval
"""

# 格力电器股票信息
GREE_ELECTRIC = {
    'code': '000651.SZ',
    'name': '格力电器',
    'exchange': 'SZSE',  # 深圳证券交易所
    'industry': '家用电器',
    'sector': '制造业'
}

# 目标查询时间
TARGET_QUERY = {
    'date': '20250730',  # 2025年7月30日
    'time': '09:30:00',  # 上午9:30
    'timezone': 'Asia/Shanghai'
}

# QMT连接配置
QMT_CONFIG = {
    'timeout': 30,  # 连接超时时间（秒）
    'retry_times': 3,  # 重试次数
    'retry_interval': 5  # 重试间隔（秒）
}

# 数据获取配置
DATA_CONFIG = {
    'periods': ['1m', '5m', '15m', '30m', '1h', '1d'],  # 支持的时间周期
    'default_period': '1m',  # 默认时间周期
    'max_records': 1000,  # 最大记录数
    'include_pre_market': True,  # 是否包含盘前数据
    'include_after_market': True  # 是否包含盘后数据
}

# 输出配置
OUTPUT_CONFIG = {
    'save_to_file': True,
    'file_format': 'json',  # 支持: json, csv, excel
    'output_dir': './data',
    'filename_template': 'gree_{date}_{time}.{format}'
}

# 显示配置
DISPLAY_CONFIG = {
    'show_basic_info': True,
    'show_market_data': True,
    'show_technical_indicators': True,
    'show_fundamental_data': True,
    'decimal_places': 4
}
