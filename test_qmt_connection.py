#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
QMT Connection Test Script
QMT连接测试脚本
"""

import sys
import datetime

def test_qmt_import():
    """Test QMT module import / 测试QMT模块导入"""
    print("Testing QMT module import...")
    print("正在测试QMT模块导入...")
    try:
        from xtquant import xtdata
        from xtquant.xttrader import XtQuantTrader
        print("✓ QMT modules imported successfully")
        print("✓ QMT模块导入成功")
        return True
    except ImportError as e:
        print(f"✗ QMT module import failed: {e}")
        print(f"✗ QMT模块导入失败: {e}")
        print("Please ensure:")
        print("请确保：")
        print("1. QMT client is properly installed")
        print("1. QMT客户端已正确安装")
        print("2. Run this script in QMT Python environment")
        print("2. 在QMT提供的Python环境中运行此脚本")
        print("3. QMT Python SDK is properly configured")
        print("3. QMT Python SDK已正确配置")
        return False

def test_qmt_connection():
    """Test QMT connection / 测试QMT连接"""
    print("\nTesting QMT connection...")
    print("正在测试QMT连接...")
    try:
        from xtquant import xtdata

        # Try to connect / 尝试连接
        session_id = xtdata.connect()

        if session_id > 0:
            print(f"✓ QMT connection successful, session ID: {session_id}")
            print(f"✓ QMT连接成功，会话ID: {session_id}")

            # Test basic functionality / 测试基本功能
            try:
                # Test data access / 获取股票列表（测试数据访问）
                print("Testing data access...")
                print("正在测试数据访问...")

                # Disconnect / 断开连接
                xtdata.disconnect()
                print("✓ QMT connection closed normally")
                print("✓ QMT连接已正常断开")
                return True

            except Exception as e:
                print(f"✗ Data access test failed: {e}")
                print(f"✗ 数据访问测试失败: {e}")
                xtdata.disconnect()
                return False
        else:
            print(f"✗ QMT connection failed, session ID: {session_id}")
            print(f"✗ QMT连接失败，会话ID: {session_id}")
            print("Please check:")
            print("请检查：")
            print("1. Is QMT client running?")
            print("1. QMT客户端是否正在运行")
            print("2. Is network connection normal?")
            print("2. 网络连接是否正常")
            print("3. Is account logged in?")
            print("3. 账户是否已登录")
            return False

    except Exception as e:
        print(f"✗ QMT connection test error: {e}")
        print(f"✗ QMT连接测试出错: {e}")
        return False

def test_gree_stock_info():
    """Test Gree Electric stock info retrieval / 测试格力电器股票信息获取"""
    print("\nTesting Gree Electric stock info retrieval...")
    print("正在测试格力电器股票信息获取...")
    try:
        from xtquant import xtdata

        session_id = xtdata.connect()
        if session_id <= 0:
            print("✗ Cannot connect to QMT, skipping stock info test")
            print("✗ 无法连接QMT，跳过股票信息测试")
            return False

        gree_code = "000651.SZ"
        print(f"Querying stock code: {gree_code}")
        print(f"正在查询股票代码: {gree_code}")

        try:
            # Get stock basic info / 获取股票基本信息
            stock_info = xtdata.get_instrument_detail(gree_code)
            if stock_info:
                print(f"✓ Successfully retrieved Gree Electric info:")
                print(f"✓ 成功获取格力电器信息:")
                print(f"  Stock code / 股票代码: {gree_code}")
                print(f"  Stock name / 股票名称: {stock_info.get('InstrumentName', 'N/A')}")
                print(f"  Exchange / 交易所: {stock_info.get('ExchangeID', 'N/A')}")
            else:
                print("✗ Failed to retrieve stock info")
                print("✗ 未能获取股票信息")

        except Exception as e:
            print(f"✗ Error retrieving stock info: {e}")
            print(f"✗ 获取股票信息时出错: {e}")

        finally:
            xtdata.disconnect()

        return True

    except Exception as e:
        print(f"✗ Stock info test error: {e}")
        print(f"✗ 股票信息测试出错: {e}")
        return False

def main():
    """Main test function / 主测试函数"""
    print("QMT Environment Test Program")
    print("QMT环境测试程序")
    print("="*50)
    print(f"Test time / 测试时间: {datetime.datetime.now()}")
    print(f"Python version / Python版本: {sys.version}")
    print("="*50)

    # Execute tests / 执行测试
    tests = [
        ("QMT Module Import / QMT模块导入", test_qmt_import),
        ("QMT Connection / QMT连接", test_qmt_connection),
        ("Gree Stock Info / 格力电器股票信息", test_gree_stock_info)
    ]

    results = []
    for test_name, test_func in tests:
        print(f"\n[{test_name} Test]")
        print(f"【{test_name}测试】")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"✗ {test_name} test exception: {e}")
            print(f"✗ {test_name}测试异常: {e}")
            results.append((test_name, False))

    # Display test results summary / 显示测试结果汇总
    print("\n" + "="*50)
    print("Test Results Summary:")
    print("测试结果汇总:")
    print("="*50)

    all_passed = True
    for test_name, result in results:
        status = "✓ PASS / 通过" if result else "✗ FAIL / 失败"
        print(f"{test_name}: {status}")
        if not result:
            all_passed = False

    print("="*50)
    if all_passed:
        print("🎉 All tests passed! You can run the main program to get Gree Electric data.")
        print("🎉 所有测试通过！可以运行主程序获取格力电器数据。")
        print("Run command / 运行命令: python qmt_gree_data.py")
    else:
        print("⚠️  Some tests failed, please resolve issues according to above hints and retry.")
        print("⚠️  部分测试失败，请根据上述提示解决问题后重试。")

    print("\nTips: If tests fail, please ensure:")
    print("提示：如果测试失败，请确保：")
    print("1. QMT client is running and logged in")
    print("1. QMT客户端正在运行且已登录")
    print("2. Run in QMT Python environment")
    print("2. 在QMT提供的Python环境中运行")
    print("3. Network connection is normal")
    print("3. 网络连接正常")
    print("4. Have appropriate data access permissions")
    print("4. 具有相应的数据访问权限")

if __name__ == "__main__":
    main()
